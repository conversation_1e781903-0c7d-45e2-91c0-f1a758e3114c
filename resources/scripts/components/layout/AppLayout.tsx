import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components/macro';
import tw from 'twin.macro';
import NavigationBar from '@/components/NavigationBar';
import Sidebar from '@/components/elements/Sidebar';
import { useTheme } from '@/context/ThemeContext';

interface AppLayoutProps {
    children: React.ReactNode;
    showSidebar?: boolean;
    sidebarVariant?: 'overlay' | 'persistent';
}

const LayoutContainer = styled.div`
    ${tw`min-h-screen flex flex-col`};
    background-color: var(--bg-primary);
`;

const MainContainer = styled.div<{ $sidebarOpen: boolean; $sidebarVariant: 'overlay' | 'persistent' }>`
    ${tw`flex flex-1`};
    
    ${({ $sidebarOpen, $sidebarVariant }) => 
        $sidebarVariant === 'persistent' && $sidebarOpen 
            ? tw`lg:ml-72` 
            : ''
    }
`;

const ContentArea = styled(motion.main)`
    ${tw`flex-1 flex flex-col overflow-hidden`};
`;

const ContentWrapper = styled.div`
    ${tw`flex-1 overflow-y-auto`};
    background-color: var(--bg-secondary);
`;

const AppLayout: React.FC<AppLayoutProps> = ({ 
    children, 
    showSidebar = true, 
    sidebarVariant = 'persistent' 
}) => {
    const { mode } = useTheme();
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // Check if we're on mobile
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 1024);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // Auto-open sidebar on desktop for persistent variant
    useEffect(() => {
        if (!isMobile && sidebarVariant === 'persistent') {
            setIsSidebarOpen(true);
        } else if (isMobile) {
            setIsSidebarOpen(false);
        }
    }, [isMobile, sidebarVariant]);

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    const closeSidebar = () => {
        setIsSidebarOpen(false);
    };

    return (
        <LayoutContainer>
            {/* Navigation Bar */}
            <NavigationBar onMenuToggle={toggleSidebar} />

            <MainContainer 
                $sidebarOpen={isSidebarOpen} 
                $sidebarVariant={sidebarVariant}
            >
                {/* Sidebar */}
                {showSidebar && (
                    <>
                        {/* Persistent Sidebar for Desktop */}
                        <Sidebar
                            isOpen={isSidebarOpen}
                            onClose={closeSidebar}
                            variant="persistent"
                        />
                        
                        {/* Overlay Sidebar for Mobile */}
                        <Sidebar
                            isOpen={isSidebarOpen}
                            onClose={closeSidebar}
                            variant="overlay"
                        />
                    </>
                )}

                {/* Main Content */}
                <ContentArea
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                >
                    <ContentWrapper>
                        {children}
                    </ContentWrapper>
                </ContentArea>
            </MainContainer>
        </LayoutContainer>
    );
};

export default AppLayout;
