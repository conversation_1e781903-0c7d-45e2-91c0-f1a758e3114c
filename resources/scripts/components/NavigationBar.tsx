import * as React from 'react';
import { useState } from 'react';
import { Link, NavLink } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faCogs,
    faLayerGroup,
    faSignOutAlt,
    faBars,
    faTimes,
    faUser,
    faChevronDown
} from '@fortawesome/free-solid-svg-icons';
import { useStoreState } from 'easy-peasy';
import { ApplicationStore } from '@/state';
import SearchContainer from '@/components/dashboard/search/SearchContainer';
import tw, { theme } from 'twin.macro';
import styled from 'styled-components/macro';
import http from '@/api/http';
import SpinnerOverlay from '@/components/elements/SpinnerOverlay';
import Tooltip from '@/components/elements/tooltip/Tooltip';
import Avatar from '@/components/Avatar';
import ThemeToggle from '@/components/elements/ThemeToggle';
import { useTheme } from '@/context/ThemeContext';

const NavigationContainer = styled(motion.nav)`
    ${tw`w-full shadow-lg relative z-50`};
    background: var(--surface-primary);
    border-bottom: 1px solid var(--border-primary);
    backdrop-filter: blur(10px);
`;

const NavigationContent = styled.div`
    ${tw`mx-auto w-full flex items-center justify-between h-16 px-4 lg:px-6`};
    max-width: 1400px;
`;

const Logo = styled(Link)`
    ${tw`flex items-center space-x-3 no-underline transition-all duration-200`};

    &:hover {
        transform: scale(1.02);
    }
`;

const LogoIcon = styled.div`
    ${tw`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold`};
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    box-shadow: var(--shadow-md);
`;

const LogoText = styled.span`
    ${tw`text-xl font-bold hidden sm:block`};
    color: var(--text-primary);
`;

const RightNavigation = styled.div`
    ${tw`flex items-center space-x-2`};

    & > a,
    & > button,
    & > .navigation-link {
        ${tw`flex items-center h-10 no-underline px-3 rounded-lg cursor-pointer transition-all duration-200 relative`};
        color: var(--text-secondary);

        &:hover {
            color: var(--text-primary);
            background-color: var(--surface-hover);
        }

        &.active {
            color: var(--primary-500);
            background-color: var(--surface-secondary);

            &::after {
                content: '';
                position: absolute;
                bottom: -1px;
                left: 50%;
                transform: translateX(-50%);
                width: 20px;
                height: 2px;
                background-color: var(--primary-500);
                border-radius: 1px;
            }
        }
    }
`;

const MobileMenuButton = styled(motion.button)`
    ${tw`lg:hidden flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200`};
    color: var(--text-secondary);
    background-color: var(--surface-secondary);

    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }
`;

const MobileMenu = styled(motion.div)`
    ${tw`lg:hidden absolute top-full left-0 right-0 shadow-xl border-t`};
    background-color: var(--surface-primary);
    border-color: var(--border-primary);
`;

const MobileMenuItem = styled(NavLink)`
    ${tw`flex items-center px-6 py-4 text-base font-medium transition-all duration-200 border-b`};
    color: var(--text-secondary);
    border-color: var(--border-primary);

    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }

    &.active {
        color: var(--primary-500);
        background-color: var(--surface-secondary);
    }

    &:last-child {
        border-bottom: none;
    }
`;

const UserDropdown = styled(motion.div)`
    ${tw`relative`};
`;

const UserButton = styled(motion.button)`
    ${tw`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200`};
    color: var(--text-secondary);

    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }
`;

const DropdownMenu = styled(motion.div)`
    ${tw`absolute right-0 mt-2 w-48 rounded-lg shadow-xl border overflow-hidden`};
    background-color: var(--surface-elevated);
    border-color: var(--border-primary);
    top: 100%;
`;

const DropdownItem = styled.button`
    ${tw`w-full flex items-center px-4 py-3 text-sm transition-all duration-200 border-b`};
    color: var(--text-secondary);
    border-color: var(--border-primary);

    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }

    &:last-child {
        border-bottom: none;
    }
`;

interface NavigationBarProps {
    onMenuToggle?: () => void;
}

const NavigationBar: React.FC<NavigationBarProps> = ({ onMenuToggle }) => {
    const name = useStoreState((state: ApplicationStore) => state.settings.data!.name);
    const rootAdmin = useStoreState((state: ApplicationStore) => state.user.data!.rootAdmin);
    const user = useStoreState((state: ApplicationStore) => state.user.data!);
    const { mode } = useTheme();
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);

    const onTriggerLogout = () => {
        setIsLoggingOut(true);
        http.post('/auth/logout').finally(() => {
            // @ts-expect-error this is valid
            window.location = '/';
        });
    };

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const toggleUserDropdown = () => {
        setIsUserDropdownOpen(!isUserDropdownOpen);
    };

    // Close dropdowns when clicking outside
    React.useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Element;
            if (!target.closest('[data-dropdown]')) {
                setIsUserDropdownOpen(false);
            }
            if (!target.closest('[data-mobile-menu]')) {
                setIsMobileMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
        <NavigationContainer
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        >
            <SpinnerOverlay visible={isLoggingOut} />
            <NavigationContent>
                {/* Menu Toggle & Logo */}
                <div className="flex items-center space-x-4">
                    {onMenuToggle && (
                        <MobileMenuButton
                            onClick={onMenuToggle}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="lg:flex"
                        >
                            <FontAwesomeIcon icon={faBars} />
                        </MobileMenuButton>
                    )}

                    <Logo to={'/'}>
                        <LogoIcon>
                            <FontAwesomeIcon icon={faLayerGroup} />
                        </LogoIcon>
                        <LogoText>{name}</LogoText>
                    </Logo>
                </div>

                {/* Desktop Navigation */}
                <div className="hidden lg:flex items-center space-x-6">
                    <SearchContainer />

                    <RightNavigation>
                        <Tooltip placement={'bottom'} content={'Dashboard'}>
                            <NavLink to={'/'} exact>
                                <FontAwesomeIcon icon={faLayerGroup} className="mr-2" />
                                <span className="hidden xl:inline">Dashboard</span>
                            </NavLink>
                        </Tooltip>

                        {rootAdmin && (
                            <Tooltip placement={'bottom'} content={'Admin Panel'}>
                                <a href={'/admin'} rel={'noreferrer'}>
                                    <FontAwesomeIcon icon={faCogs} className="mr-2" />
                                    <span className="hidden xl:inline">Admin</span>
                                </a>
                            </Tooltip>
                        )}

                        <ThemeToggle size="sm" />

                        {/* User Dropdown */}
                        <UserDropdown data-dropdown>
                            <UserButton
                                onClick={toggleUserDropdown}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <div className="w-8 h-8 rounded-full overflow-hidden">
                                    <Avatar.User />
                                </div>
                                <span className="hidden xl:inline font-medium">
                                    {user.username}
                                </span>
                                <FontAwesomeIcon
                                    icon={faChevronDown}
                                    className={`transition-transform duration-200 ${
                                        isUserDropdownOpen ? 'rotate-180' : ''
                                    }`}
                                />
                            </UserButton>

                            <AnimatePresence>
                                {isUserDropdownOpen && (
                                    <DropdownMenu
                                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                        animate={{ opacity: 1, y: 0, scale: 1 }}
                                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <DropdownItem
                                            as={Link}
                                            to="/account"
                                            onClick={() => setIsUserDropdownOpen(false)}
                                        >
                                            <FontAwesomeIcon icon={faUser} className="mr-3" />
                                            Account Settings
                                        </DropdownItem>
                                        <DropdownItem onClick={onTriggerLogout}>
                                            <FontAwesomeIcon icon={faSignOutAlt} className="mr-3" />
                                            Sign Out
                                        </DropdownItem>
                                    </DropdownMenu>
                                )}
                            </AnimatePresence>
                        </UserDropdown>
                    </RightNavigation>
                </div>

                {/* Mobile Menu Button */}
                <MobileMenuButton
                    onClick={toggleMobileMenu}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    data-mobile-menu
                >
                    <FontAwesomeIcon icon={isMobileMenuOpen ? faTimes : faBars} />
                </MobileMenuButton>
            </NavigationContent>

            {/* Mobile Menu */}
            <AnimatePresence>
                {isMobileMenuOpen && (
                    <MobileMenu
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        data-mobile-menu
                    >
                        <div className="px-4 py-2">
                            <SearchContainer />
                        </div>

                        <MobileMenuItem
                            to="/"
                            exact
                            onClick={() => setIsMobileMenuOpen(false)}
                        >
                            <FontAwesomeIcon icon={faLayerGroup} className="mr-3" />
                            Dashboard
                        </MobileMenuItem>

                        {rootAdmin && (
                            <div className="px-6 py-4 border-b border-theme-primary">
                                <a
                                    href="/admin"
                                    rel="noreferrer"
                                    className="flex items-center text-theme-secondary hover:text-theme-primary transition-colors duration-200"
                                >
                                    <FontAwesomeIcon icon={faCogs} className="mr-3" />
                                    Admin Panel
                                </a>
                            </div>
                        )}

                        <MobileMenuItem
                            to="/account"
                            onClick={() => setIsMobileMenuOpen(false)}
                        >
                            <FontAwesomeIcon icon={faUser} className="mr-3" />
                            Account Settings
                        </MobileMenuItem>

                        <div className="px-6 py-4 border-b border-theme-primary">
                            <div className="flex items-center justify-between">
                                <span className="text-theme-secondary">Theme</span>
                                <ThemeToggle size="sm" />
                            </div>
                        </div>

                        <div className="px-6 py-4">
                            <button
                                onClick={onTriggerLogout}
                                className="flex items-center text-red-500 hover:text-red-600 transition-colors duration-200"
                            >
                                <FontAwesomeIcon icon={faSignOutAlt} className="mr-3" />
                                Sign Out
                            </button>
                        </div>
                    </MobileMenu>
                )}
            </AnimatePresence>
        </NavigationContainer>
    );
};

export default NavigationBar;
