import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NavLink, useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faLayerGroup,
    faServer,
    faUsers,
    faDatabase,
    faCogs,
    faChartLine,
    faFileAlt,
    faShield,
    faNetworkWired,
    faChevronLeft,
    faChevronRight,
    faTimes
} from '@fortawesome/free-solid-svg-icons';
import styled from 'styled-components/macro';
import tw from 'twin.macro';
import { useTheme } from '@/context/ThemeContext';

interface SidebarProps {
    isOpen: boolean;
    onClose: () => void;
    variant?: 'overlay' | 'persistent';
}

interface MenuItem {
    name: string;
    path: string;
    icon: any;
    exact?: boolean;
    adminOnly?: boolean;
    children?: MenuItem[];
}

const SidebarContainer = styled(motion.aside)<{ $variant: 'overlay' | 'persistent' }>`
    ${tw`fixed top-0 left-0 h-full z-40 flex flex-col shadow-xl`};
    width: 280px;
    background-color: var(--surface-primary);
    border-right: 1px solid var(--border-primary);
    
    ${({ $variant }) => $variant === 'overlay' && tw`lg:hidden`}
    ${({ $variant }) => $variant === 'persistent' && tw`hidden lg:flex`}
`;

const SidebarOverlay = styled(motion.div)`
    ${tw`fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden`};
`;

const SidebarHeader = styled.div`
    ${tw`flex items-center justify-between p-6 border-b`};
    border-color: var(--border-primary);
`;

const SidebarContent = styled.div`
    ${tw`flex-1 overflow-y-auto py-4`};
`;

const SidebarFooter = styled.div`
    ${tw`p-4 border-t`};
    border-color: var(--border-primary);
`;

const MenuSection = styled.div`
    ${tw`mb-6`};
`;

const MenuSectionTitle = styled.h3`
    ${tw`px-6 mb-3 text-xs font-semibold uppercase tracking-wider`};
    color: var(--text-tertiary);
`;

const MenuItem = styled(NavLink)`
    ${tw`flex items-center px-6 py-3 text-sm font-medium transition-all duration-200 relative`};
    color: var(--text-secondary);
    
    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }
    
    &.active {
        color: var(--primary-500);
        background-color: var(--surface-secondary);
        
        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--primary-500);
        }
    }
`;

const MenuIcon = styled.div`
    ${tw`w-5 h-5 mr-3 flex items-center justify-center`};
`;

const CollapseButton = styled(motion.button)`
    ${tw`p-2 rounded-lg transition-all duration-200`};
    color: var(--text-secondary);
    background-color: var(--surface-secondary);
    
    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }
`;

const CloseButton = styled(motion.button)`
    ${tw`p-2 rounded-lg transition-all duration-200 lg:hidden`};
    color: var(--text-secondary);
    
    &:hover {
        color: var(--text-primary);
        background-color: var(--surface-hover);
    }
`;

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, variant = 'overlay' }) => {
    const { colors } = useTheme();
    const location = useLocation();
    const [isCollapsed, setIsCollapsed] = useState(false);

    const menuItems: MenuItem[] = [
        {
            name: 'Dashboard',
            path: '/',
            icon: faLayerGroup,
            exact: true,
        },
        {
            name: 'Servers',
            path: '/servers',
            icon: faServer,
        },
        {
            name: 'Users',
            path: '/users',
            icon: faUsers,
            adminOnly: true,
        },
        {
            name: 'Nodes',
            path: '/nodes',
            icon: faNetworkWired,
            adminOnly: true,
        },
        {
            name: 'Databases',
            path: '/databases',
            icon: faDatabase,
            adminOnly: true,
        },
        {
            name: 'Analytics',
            path: '/analytics',
            icon: faChartLine,
            adminOnly: true,
        },
        {
            name: 'Logs',
            path: '/logs',
            icon: faFileAlt,
            adminOnly: true,
        },
        {
            name: 'Security',
            path: '/security',
            icon: faShield,
            adminOnly: true,
        },
        {
            name: 'Settings',
            path: '/settings',
            icon: faCogs,
            adminOnly: true,
        },
    ];

    const sidebarVariants = {
        open: {
            x: 0,
            transition: {
                type: 'spring',
                stiffness: 300,
                damping: 30,
            },
        },
        closed: {
            x: -280,
            transition: {
                type: 'spring',
                stiffness: 300,
                damping: 30,
            },
        },
    };

    const overlayVariants = {
        open: {
            opacity: 1,
            transition: { duration: 0.2 },
        },
        closed: {
            opacity: 0,
            transition: { duration: 0.2 },
        },
    };

    return (
        <>
            {/* Overlay for mobile */}
            {variant === 'overlay' && (
                <AnimatePresence>
                    {isOpen && (
                        <SidebarOverlay
                            variants={overlayVariants}
                            initial="closed"
                            animate="open"
                            exit="closed"
                            onClick={onClose}
                        />
                    )}
                </AnimatePresence>
            )}

            {/* Sidebar */}
            <SidebarContainer
                $variant={variant}
                variants={sidebarVariants}
                initial={variant === 'overlay' ? 'closed' : 'open'}
                animate={isOpen ? 'open' : 'closed'}
            >
                {/* Header */}
                <SidebarHeader>
                    <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white font-bold">
                            P
                        </div>
                        {!isCollapsed && (
                            <span className="text-lg font-semibold text-theme-primary">
                                Pterodactyl
                            </span>
                        )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        {variant === 'persistent' && (
                            <CollapseButton
                                onClick={() => setIsCollapsed(!isCollapsed)}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <FontAwesomeIcon 
                                    icon={isCollapsed ? faChevronRight : faChevronLeft} 
                                />
                            </CollapseButton>
                        )}
                        
                        <CloseButton
                            onClick={onClose}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <FontAwesomeIcon icon={faTimes} />
                        </CloseButton>
                    </div>
                </SidebarHeader>

                {/* Content */}
                <SidebarContent>
                    <MenuSection>
                        {!isCollapsed && (
                            <MenuSectionTitle>Main</MenuSectionTitle>
                        )}
                        
                        {menuItems
                            .filter(item => !item.adminOnly)
                            .map((item) => (
                                <MenuItem
                                    key={item.path}
                                    to={item.path}
                                    exact={item.exact}
                                    onClick={() => variant === 'overlay' && onClose()}
                                >
                                    <MenuIcon>
                                        <FontAwesomeIcon icon={item.icon} />
                                    </MenuIcon>
                                    {!isCollapsed && item.name}
                                </MenuItem>
                            ))}
                    </MenuSection>

                    <MenuSection>
                        {!isCollapsed && (
                            <MenuSectionTitle>Administration</MenuSectionTitle>
                        )}
                        
                        {menuItems
                            .filter(item => item.adminOnly)
                            .map((item) => (
                                <MenuItem
                                    key={item.path}
                                    to={item.path}
                                    exact={item.exact}
                                    onClick={() => variant === 'overlay' && onClose()}
                                >
                                    <MenuIcon>
                                        <FontAwesomeIcon icon={item.icon} />
                                    </MenuIcon>
                                    {!isCollapsed && item.name}
                                </MenuItem>
                            ))}
                    </MenuSection>
                </SidebarContent>

                {/* Footer */}
                <SidebarFooter>
                    {!isCollapsed && (
                        <div className="text-xs text-theme-tertiary">
                            <p>Pterodactyl Panel</p>
                            <p>v1.11.0</p>
                        </div>
                    )}
                </SidebarFooter>
            </SidebarContainer>
        </>
    );
};

export default Sidebar;
