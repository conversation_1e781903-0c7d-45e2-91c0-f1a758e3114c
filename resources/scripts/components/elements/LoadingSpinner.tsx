import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components/macro';
import tw from 'twin.macro';

interface LoadingSpinnerProps {
    size?: 'sm' | 'md' | 'lg' | 'xl';
    variant?: 'default' | 'dots' | 'pulse' | 'bars' | 'ring';
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
    className?: string;
    centered?: boolean;
}

const SpinnerContainer = styled.div<{ $centered: boolean }>`
    ${({ $centered }) => $centered && tw`flex items-center justify-center`}
`;

const DefaultSpinner = styled(motion.div)<{ $size: string; $color: string }>`
    ${tw`rounded-full border-2 border-transparent`};
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`w-4 h-4`;
            case 'md':
                return tw`w-6 h-6`;
            case 'lg':
                return tw`w-8 h-8`;
            case 'xl':
                return tw`w-12 h-12`;
            default:
                return tw`w-6 h-6`;
        }
    }}
    
    ${({ $color }) => {
        switch ($color) {
            case 'primary':
                return `border-top-color: var(--primary-500); border-right-color: var(--primary-200);`;
            case 'secondary':
                return `border-top-color: var(--text-secondary); border-right-color: var(--text-disabled);`;
            case 'success':
                return `border-top-color: var(--status-success); border-right-color: rgba(16, 185, 129, 0.3);`;
            case 'warning':
                return `border-top-color: var(--status-warning); border-right-color: rgba(245, 158, 11, 0.3);`;
            case 'error':
                return `border-top-color: var(--status-error); border-right-color: rgba(239, 68, 68, 0.3);`;
            default:
                return `border-top-color: var(--primary-500); border-right-color: var(--primary-200);`;
        }
    }}
`;

const DotsContainer = styled.div<{ $size: string }>`
    ${tw`flex space-x-1`};
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`space-x-0.5`;
            case 'md':
                return tw`space-x-1`;
            case 'lg':
                return tw`space-x-1.5`;
            case 'xl':
                return tw`space-x-2`;
            default:
                return tw`space-x-1`;
        }
    }}
`;

const Dot = styled(motion.div)<{ $size: string; $color: string }>`
    ${tw`rounded-full`};
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`w-1 h-1`;
            case 'md':
                return tw`w-2 h-2`;
            case 'lg':
                return tw`w-3 h-3`;
            case 'xl':
                return tw`w-4 h-4`;
            default:
                return tw`w-2 h-2`;
        }
    }}
    
    ${({ $color }) => {
        switch ($color) {
            case 'primary':
                return `background-color: var(--primary-500);`;
            case 'secondary':
                return `background-color: var(--text-secondary);`;
            case 'success':
                return `background-color: var(--status-success);`;
            case 'warning':
                return `background-color: var(--status-warning);`;
            case 'error':
                return `background-color: var(--status-error);`;
            default:
                return `background-color: var(--primary-500);`;
        }
    }}
`;

const PulseCircle = styled(motion.div)<{ $size: string; $color: string }>`
    ${tw`rounded-full`};
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`w-4 h-4`;
            case 'md':
                return tw`w-6 h-6`;
            case 'lg':
                return tw`w-8 h-8`;
            case 'xl':
                return tw`w-12 h-12`;
            default:
                return tw`w-6 h-6`;
        }
    }}
    
    ${({ $color }) => {
        switch ($color) {
            case 'primary':
                return `background-color: var(--primary-500);`;
            case 'secondary':
                return `background-color: var(--text-secondary);`;
            case 'success':
                return `background-color: var(--status-success);`;
            case 'warning':
                return `background-color: var(--status-warning);`;
            case 'error':
                return `background-color: var(--status-error);`;
            default:
                return `background-color: var(--primary-500);`;
        }
    }}
`;

const BarsContainer = styled.div<{ $size: string }>`
    ${tw`flex items-end space-x-1`};
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`space-x-0.5`;
            case 'md':
                return tw`space-x-1`;
            case 'lg':
                return tw`space-x-1.5`;
            case 'xl':
                return tw`space-x-2`;
            default:
                return tw`space-x-1`;
        }
    }}
`;

const Bar = styled(motion.div)<{ $size: string; $color: string }>`
    ${tw`rounded-sm`};
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`w-0.5`;
            case 'md':
                return tw`w-1`;
            case 'lg':
                return tw`w-1.5`;
            case 'xl':
                return tw`w-2`;
            default:
                return tw`w-1`;
        }
    }}
    
    ${({ $color }) => {
        switch ($color) {
            case 'primary':
                return `background-color: var(--primary-500);`;
            case 'secondary':
                return `background-color: var(--text-secondary);`;
            case 'success':
                return `background-color: var(--status-success);`;
            case 'warning':
                return `background-color: var(--status-warning);`;
            case 'error':
                return `background-color: var(--status-error);`;
            default:
                return `background-color: var(--primary-500);`;
        }
    }}
`;

const RingSpinner = styled(motion.div)<{ $size: string; $color: string }>`
    ${tw`rounded-full border-2`};
    border-color: transparent;
    
    ${({ $size }) => {
        switch ($size) {
            case 'sm':
                return tw`w-4 h-4`;
            case 'md':
                return tw`w-6 h-6`;
            case 'lg':
                return tw`w-8 h-8`;
            case 'xl':
                return tw`w-12 h-12`;
            default:
                return tw`w-6 h-6`;
        }
    }}
    
    ${({ $color }) => {
        switch ($color) {
            case 'primary':
                return `border-top-color: var(--primary-500); border-left-color: var(--primary-500);`;
            case 'secondary':
                return `border-top-color: var(--text-secondary); border-left-color: var(--text-secondary);`;
            case 'success':
                return `border-top-color: var(--status-success); border-left-color: var(--status-success);`;
            case 'warning':
                return `border-top-color: var(--status-warning); border-left-color: var(--status-warning);`;
            case 'error':
                return `border-top-color: var(--status-error); border-left-color: var(--status-error);`;
            default:
                return `border-top-color: var(--primary-500); border-left-color: var(--primary-500);`;
        }
    }}
`;

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
    size = 'md',
    variant = 'default',
    color = 'primary',
    className,
    centered = false,
}) => {
    const getBarHeight = (size: string) => {
        switch (size) {
            case 'sm':
                return [8, 12, 16, 12, 8];
            case 'md':
                return [12, 18, 24, 18, 12];
            case 'lg':
                return [16, 24, 32, 24, 16];
            case 'xl':
                return [20, 30, 40, 30, 20];
            default:
                return [12, 18, 24, 18, 12];
        }
    };

    const renderSpinner = () => {
        switch (variant) {
            case 'dots':
                return (
                    <DotsContainer $size={size}>
                        {[0, 1, 2].map((index) => (
                            <Dot
                                key={index}
                                $size={size}
                                $color={color}
                                animate={{
                                    scale: [1, 1.5, 1],
                                    opacity: [0.5, 1, 0.5],
                                }}
                                transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    delay: index * 0.2,
                                }}
                            />
                        ))}
                    </DotsContainer>
                );

            case 'pulse':
                return (
                    <PulseCircle
                        $size={size}
                        $color={color}
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.7, 1, 0.7],
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: 'easeInOut',
                        }}
                    />
                );

            case 'bars':
                return (
                    <BarsContainer $size={size}>
                        {getBarHeight(size).map((height, index) => (
                            <Bar
                                key={index}
                                $size={size}
                                $color={color}
                                animate={{
                                    height: [height * 0.5, height, height * 0.5],
                                }}
                                transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    delay: index * 0.1,
                                }}
                                style={{ height: height * 0.5 }}
                            />
                        ))}
                    </BarsContainer>
                );

            case 'ring':
                return (
                    <RingSpinner
                        $size={size}
                        $color={color}
                        animate={{ rotate: 360 }}
                        transition={{
                            duration: 1,
                            repeat: Infinity,
                            ease: 'linear',
                        }}
                    />
                );

            default:
                return (
                    <DefaultSpinner
                        $size={size}
                        $color={color}
                        animate={{ rotate: 360 }}
                        transition={{
                            duration: 1,
                            repeat: Infinity,
                            ease: 'linear',
                        }}
                    />
                );
        }
    };

    return (
        <SpinnerContainer $centered={centered} className={className}>
            {renderSpinner()}
        </SpinnerContainer>
    );
};

export default LoadingSpinner;
