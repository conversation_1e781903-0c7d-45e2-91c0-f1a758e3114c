import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSun, faMoon } from '@fortawesome/free-solid-svg-icons';
import styled from 'styled-components/macro';
import tw from 'twin.macro';
import { useTheme } from '@/context/ThemeContext';

const ToggleContainer = styled(motion.button)`
    ${tw`relative flex items-center justify-center w-12 h-6 rounded-full border-2 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent`};
    background: var(--surface-secondary);
    border-color: var(--border-primary);
    
    &:hover {
        background: var(--surface-hover);
        border-color: var(--border-secondary);
    }
    
    &:focus {
        border-color: var(--border-focus);
        box-shadow: 0 0 0 2px var(--border-focus);
    }
`;

const ToggleThumb = styled(motion.div)`
    ${tw`absolute w-4 h-4 rounded-full flex items-center justify-center text-xs`};
    background: var(--primary-500);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
`;

const IconWrapper = styled(motion.div)`
    ${tw`flex items-center justify-center w-full h-full`};
`;

interface ThemeToggleProps {
    className?: string;
    size?: 'sm' | 'md' | 'lg';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className, size = 'md' }) => {
    const { mode, toggleTheme } = useTheme();
    const isDark = mode === 'dark';

    const sizeClasses = {
        sm: 'w-8 h-4',
        md: 'w-12 h-6',
        lg: 'w-16 h-8',
    };

    const thumbSizeClasses = {
        sm: 'w-3 h-3',
        md: 'w-4 h-4',
        lg: 'w-6 h-6',
    };

    const iconSizeClasses = {
        sm: 'text-2xs',
        md: 'text-xs',
        lg: 'text-sm',
    };

    return (
        <ToggleContainer
            className={`${sizeClasses[size]} ${className || ''}`}
            onClick={toggleTheme}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={false}
            animate={{
                backgroundColor: isDark ? 'var(--surface-secondary)' : 'var(--surface-secondary)',
            }}
            transition={{
                type: 'spring',
                stiffness: 500,
                damping: 30,
            }}
        >
            <ToggleThumb
                className={thumbSizeClasses[size]}
                animate={{
                    x: isDark ? '100%' : '0%',
                    backgroundColor: isDark ? 'var(--primary-400)' : 'var(--primary-600)',
                }}
                transition={{
                    type: 'spring',
                    stiffness: 500,
                    damping: 30,
                }}
            >
                <AnimatePresence mode="wait">
                    <IconWrapper
                        key={isDark ? 'moon' : 'sun'}
                        className={iconSizeClasses[size]}
                        initial={{ opacity: 0, rotate: -180 }}
                        animate={{ opacity: 1, rotate: 0 }}
                        exit={{ opacity: 0, rotate: 180 }}
                        transition={{ duration: 0.2 }}
                    >
                        <FontAwesomeIcon 
                            icon={isDark ? faMoon : faSun} 
                            className={isDark ? 'text-blue-200' : 'text-yellow-200'}
                        />
                    </IconWrapper>
                </AnimatePresence>
            </ToggleThumb>
        </ToggleContainer>
    );
};

export default ThemeToggle;
