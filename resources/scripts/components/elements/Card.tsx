import React, { ReactNode } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import styled from 'styled-components/macro';
import tw from 'twin.macro';

interface CardProps extends Omit<HTMLMotionProps<'div'>, 'children'> {
    children: ReactNode;
    variant?: 'default' | 'elevated' | 'outlined' | 'glass';
    padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
    hover?: boolean;
    clickable?: boolean;
    className?: string;
}

const CardContainer = styled(motion.div)<{
    $variant: CardProps['variant'];
    $padding: CardProps['padding'];
    $hover: boolean;
    $clickable: boolean;
}>`
    ${tw`rounded-xl transition-all duration-300 relative overflow-hidden`};
    
    /* Base styles */
    background-color: var(--surface-primary);
    border: 1px solid var(--border-primary);
    
    /* Variant styles */
    ${({ $variant }) => {
        switch ($variant) {
            case 'elevated':
                return `
                    background-color: var(--surface-elevated);
                    box-shadow: var(--shadow-lg);
                    border: none;
                `;
            case 'outlined':
                return `
                    background-color: transparent;
                    border: 2px solid var(--border-secondary);
                `;
            case 'glass':
                return `
                    background: rgba(255, 255, 255, 0.05);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    box-shadow: var(--shadow-xl);
                `;
            default:
                return `
                    background-color: var(--surface-primary);
                    box-shadow: var(--shadow-sm);
                `;
        }
    }}
    
    /* Padding styles */
    ${({ $padding }) => {
        switch ($padding) {
            case 'none':
                return tw`p-0`;
            case 'sm':
                return tw`p-3`;
            case 'md':
                return tw`p-4`;
            case 'lg':
                return tw`p-6`;
            case 'xl':
                return tw`p-8`;
            default:
                return tw`p-4`;
        }
    }}
    
    /* Hover effects */
    ${({ $hover, $clickable }) => {
        if ($hover || $clickable) {
            return `
                &:hover {
                    transform: translateY(-2px);
                    box-shadow: var(--shadow-xl);
                    border-color: var(--border-secondary);
                }
            `;
        }
        return '';
    }}
    
    /* Clickable cursor */
    ${({ $clickable }) => $clickable && tw`cursor-pointer`}
    
    /* Focus styles for accessibility */
    &:focus-visible {
        outline: 2px solid var(--border-focus);
        outline-offset: 2px;
    }
`;

const Card: React.FC<CardProps> = ({
    children,
    variant = 'default',
    padding = 'md',
    hover = false,
    clickable = false,
    className,
    ...motionProps
}) => {
    const defaultAnimation = {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -20 },
        transition: {
            type: 'spring',
            stiffness: 300,
            damping: 30,
        },
    };

    return (
        <CardContainer
            $variant={variant}
            $padding={padding}
            $hover={hover}
            $clickable={clickable}
            className={className}
            {...defaultAnimation}
            {...motionProps}
        >
            {children}
        </CardContainer>
    );
};

// Card Header Component
interface CardHeaderProps {
    children: ReactNode;
    className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => (
    <div className={`border-b border-theme-primary pb-3 mb-4 ${className || ''}`}>
        {children}
    </div>
);

// Card Title Component
interface CardTitleProps {
    children: ReactNode;
    className?: string;
    size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const CardTitle: React.FC<CardTitleProps> = ({ children, className, size = 'md' }) => {
    const sizeClasses = {
        sm: 'text-sm font-medium',
        md: 'text-lg font-semibold',
        lg: 'text-xl font-semibold',
        xl: 'text-2xl font-bold',
    };

    return (
        <h3 className={`text-theme-primary ${sizeClasses[size]} ${className || ''}`}>
            {children}
        </h3>
    );
};

// Card Content Component
interface CardContentProps {
    children: ReactNode;
    className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({ children, className }) => (
    <div className={`text-theme-secondary ${className || ''}`}>
        {children}
    </div>
);

// Card Footer Component
interface CardFooterProps {
    children: ReactNode;
    className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className }) => (
    <div className={`border-t border-theme-primary pt-3 mt-4 ${className || ''}`}>
        {children}
    </div>
);

// Card Actions Component
interface CardActionsProps {
    children: ReactNode;
    className?: string;
    align?: 'left' | 'center' | 'right' | 'between';
}

export const CardActions: React.FC<CardActionsProps> = ({ children, className, align = 'right' }) => {
    const alignClasses = {
        left: 'justify-start',
        center: 'justify-center',
        right: 'justify-end',
        between: 'justify-between',
    };

    return (
        <div className={`flex items-center space-x-2 ${alignClasses[align]} ${className || ''}`}>
            {children}
        </div>
    );
};

export default Card;
