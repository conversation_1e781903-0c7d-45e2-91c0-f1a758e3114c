import React, { memo, useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
    faEthernet, 
    faHdd, 
    faMemory, 
    faMicrochip, 
    faServer, 
    faPlay, 
    faStop, 
    faRedo,
    faExclamationTriangle,
    faCheckCircle,
    faClock
} from '@fortawesome/free-solid-svg-icons';
import { Link } from 'react-router-dom';
import { Server } from '@/api/server/getServer';
import getServerResourceUsage, { ServerPowerState, ServerStats } from '@/api/server/getServerResourceUsage';
import { bytesToString, ip, mbToBytes } from '@/lib/formatters';
import tw from 'twin.macro';
import styled from 'styled-components/macro';
import isEqual from 'react-fast-compare';
import <PERSON>, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardActions } from '@/components/elements/Card';
import { useTheme } from '@/context/ThemeContext';

// Determines if the current value is in an alarm threshold
const isAlarmState = (current: number, limit: number): boolean => limit > 0 && current / (limit * 1024 * 1024) >= 0.9;

const StatusIndicator = styled(motion.div)<{ $status: ServerPowerState | undefined }>`
    ${tw`w-3 h-3 rounded-full flex-shrink-0`};
    
    ${({ $status }) => {
        switch ($status) {
            case 'running':
                return `background-color: var(--status-success);`;
            case 'offline':
                return `background-color: var(--status-error);`;
            case 'starting':
            case 'stopping':
                return `background-color: var(--status-warning);`;
            default:
                return `background-color: var(--text-disabled);`;
        }
    }}
`;

const ResourceBar = styled.div<{ $percentage: number; $alarm: boolean }>`
    ${tw`w-full h-2 rounded-full overflow-hidden`};
    background-color: var(--surface-tertiary);
    
    &::after {
        content: '';
        display: block;
        height: 100%;
        width: ${({ $percentage }) => Math.min($percentage, 100)}%;
        background-color: ${({ $alarm }) => 
            $alarm ? 'var(--status-error)' : 'var(--primary-500)'};
        transition: width 0.3s ease, background-color 0.3s ease;
        border-radius: inherit;
    }
`;

const ResourceMetric = styled.div`
    ${tw`flex items-center justify-between text-xs`};
    
    .icon {
        ${tw`mr-2 flex-shrink-0`};
        color: var(--text-tertiary);
    }
    
    .value {
        ${tw`font-medium`};
        color: var(--text-primary);
    }
    
    .limit {
        color: var(--text-tertiary);
    }
`;

const ServerIcon = styled.div`
    ${tw`w-12 h-12 rounded-lg flex items-center justify-center text-xl flex-shrink-0`};
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
`;

const QuickActionButton = styled(motion.button)`
    ${tw`p-2 rounded-lg text-xs transition-all duration-200 flex items-center justify-center`};
    background-color: var(--surface-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
    
    &:hover {
        background-color: var(--surface-hover);
        color: var(--text-primary);
        border-color: var(--border-secondary);
    }
    
    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
`;

type Timer = ReturnType<typeof setInterval>;

interface ServerCardProps {
    server: Server;
    className?: string;
}

const ServerCard: React.FC<ServerCardProps> = memo(({ server, className }) => {
    const { colors } = useTheme();
    const interval = useRef<Timer>(null) as React.MutableRefObject<Timer>;
    const [isSuspended, setIsSuspended] = useState(server.status === 'suspended');
    const [stats, setStats] = useState<ServerStats | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const getStats = () => {
        setIsLoading(true);
        return getServerResourceUsage(server.uuid)
            .then((data) => setStats(data))
            .catch((error) => console.error(error))
            .finally(() => setIsLoading(false));
    };

    useEffect(() => {
        setIsSuspended(stats?.isSuspended || server.status === 'suspended');
    }, [stats?.isSuspended, server.status]);

    useEffect(() => {
        if (isSuspended) return;

        getStats().then(() => {
            interval.current = setInterval(() => getStats(), 30000);
        });

        return () => {
            interval.current && clearInterval(interval.current);
        };
    }, [isSuspended]);

    const alarms = { cpu: false, memory: false, disk: false };
    if (stats) {
        alarms.cpu = server.limits.cpu === 0 ? false : stats.cpuUsagePercent >= server.limits.cpu * 0.9;
        alarms.memory = isAlarmState(stats.memoryUsageInBytes, server.limits.memory);
        alarms.disk = server.limits.disk === 0 ? false : isAlarmState(stats.diskUsageInBytes, server.limits.disk);
    }

    const getStatusIcon = (status: ServerPowerState | undefined) => {
        switch (status) {
            case 'running':
                return faCheckCircle;
            case 'offline':
                return faExclamationTriangle;
            case 'starting':
            case 'stopping':
                return faClock;
            default:
                return faServer;
        }
    };

    const getStatusText = (status: ServerPowerState | undefined) => {
        switch (status) {
            case 'running':
                return 'Online';
            case 'offline':
                return 'Offline';
            case 'starting':
                return 'Starting';
            case 'stopping':
                return 'Stopping';
            default:
                return 'Unknown';
        }
    };

    const diskLimit = server.limits.disk !== 0 ? bytesToString(mbToBytes(server.limits.disk)) : 'Unlimited';
    const memoryLimit = server.limits.memory !== 0 ? bytesToString(mbToBytes(server.limits.memory)) : 'Unlimited';
    const cpuLimit = server.limits.cpu !== 0 ? server.limits.cpu + '%' : 'Unlimited';

    return (
        <Card
            variant="elevated"
            hover
            clickable
            className={className}
            as={motion.div}
            whileHover={{ y: -4 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        >
            <Link to={`/server/${server.id}`} className="block">
                <CardHeader>
                    <div className="flex items-start space-x-4">
                        <ServerIcon>
                            <FontAwesomeIcon icon={faServer} />
                        </ServerIcon>
                        
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                                <CardTitle size="md" className="truncate">
                                    {server.name}
                                </CardTitle>
                                <StatusIndicator
                                    $status={stats?.status}
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                />
                            </div>
                            
                            {server.description && (
                                <p className="text-sm text-theme-tertiary line-clamp-2 mb-2">
                                    {server.description}
                                </p>
                            )}
                            
                            <div className="flex items-center space-x-2 text-xs text-theme-tertiary">
                                <FontAwesomeIcon icon={getStatusIcon(stats?.status)} />
                                <span>{getStatusText(stats?.status)}</span>
                                {server.allocations
                                    .filter((alloc) => alloc.isDefault)
                                    .map((allocation) => (
                                        <span key={allocation.ip + allocation.port.toString()}>
                                            • {allocation.alias || ip(allocation.ip)}:{allocation.port}
                                        </span>
                                    ))}
                            </div>
                        </div>
                    </div>
                </CardHeader>
            </Link>

            <CardContent>
                {isSuspended ? (
                    <div className="text-center py-4">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            {server.status === 'suspended' ? 'Suspended' : 'Connection Error'}
                        </span>
                    </div>
                ) : server.isTransferring || server.status ? (
                    <div className="text-center py-4">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                            {server.isTransferring
                                ? 'Transferring'
                                : server.status === 'installing'
                                ? 'Installing'
                                : server.status === 'restoring_backup'
                                ? 'Restoring Backup'
                                : 'Unavailable'}
                        </span>
                    </div>
                ) : stats ? (
                    <div className="space-y-3">
                        {/* CPU Usage */}
                        <div>
                            <ResourceMetric>
                                <div className="flex items-center">
                                    <FontAwesomeIcon icon={faMicrochip} className="icon" />
                                    <span className="value">{stats.cpuUsagePercent.toFixed(1)}%</span>
                                </div>
                                <span className="limit">of {cpuLimit}</span>
                            </ResourceMetric>
                            <ResourceBar 
                                $percentage={stats.cpuUsagePercent} 
                                $alarm={alarms.cpu}
                            />
                        </div>

                        {/* Memory Usage */}
                        <div>
                            <ResourceMetric>
                                <div className="flex items-center">
                                    <FontAwesomeIcon icon={faMemory} className="icon" />
                                    <span className="value">{bytesToString(stats.memoryUsageInBytes)}</span>
                                </div>
                                <span className="limit">of {memoryLimit}</span>
                            </ResourceMetric>
                            <ResourceBar 
                                $percentage={server.limits.memory > 0 ? (stats.memoryUsageInBytes / mbToBytes(server.limits.memory)) * 100 : 0} 
                                $alarm={alarms.memory}
                            />
                        </div>

                        {/* Disk Usage */}
                        <div>
                            <ResourceMetric>
                                <div className="flex items-center">
                                    <FontAwesomeIcon icon={faHdd} className="icon" />
                                    <span className="value">{bytesToString(stats.diskUsageInBytes)}</span>
                                </div>
                                <span className="limit">of {diskLimit}</span>
                            </ResourceMetric>
                            <ResourceBar 
                                $percentage={server.limits.disk > 0 ? (stats.diskUsageInBytes / mbToBytes(server.limits.disk)) * 100 : 0} 
                                $alarm={alarms.disk}
                            />
                        </div>
                    </div>
                ) : (
                    <div className="flex items-center justify-center py-8">
                        <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                            className="w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full"
                        />
                    </div>
                )}
            </CardContent>

            {!isSuspended && stats && (
                <CardActions align="between" className="pt-3 border-t border-theme-primary">
                    <div className="flex space-x-2">
                        <QuickActionButton
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            disabled={stats.status !== 'offline'}
                        >
                            <FontAwesomeIcon icon={faPlay} />
                        </QuickActionButton>
                        
                        <QuickActionButton
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            disabled={stats.status !== 'running'}
                        >
                            <FontAwesomeIcon icon={faStop} />
                        </QuickActionButton>
                        
                        <QuickActionButton
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <FontAwesomeIcon icon={faRedo} />
                        </QuickActionButton>
                    </div>
                    
                    <Link 
                        to={`/server/${server.id}`}
                        className="text-xs text-primary-500 hover:text-primary-600 font-medium"
                    >
                        Manage →
                    </Link>
                </CardActions>
            )}
        </Card>
    );
}, isEqual);

export default ServerCard;
