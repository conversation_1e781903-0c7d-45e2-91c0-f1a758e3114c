import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faServer,
    faUsers,
    faDatabase,
    faChartLine,
    faExclamationTriangle,
    faCheckCircle,
    faClock,
    faHdd,
    faMemory,
    faMicrochip,
    faNetworkWired,
    faPlus,
    faCog,
    faDownload,
    faUpload
} from '@fortawesome/free-solid-svg-icons';
import styled from 'styled-components/macro';
import tw from 'twin.macro';
import Card, { CardHeader, CardTitle, CardContent, CardActions } from '@/components/elements/Card';
import { useTheme } from '@/context/ThemeContext';

const AdminContainer = styled.div`
    ${tw`min-h-screen p-6`};
    background-color: var(--bg-primary);
`;

const StatsGrid = styled.div`
    ${tw`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8`};
`;

const ChartsGrid = styled.div`
    ${tw`grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8`};
`;

const QuickActionsGrid = styled.div`
    ${tw`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6`};
`;

const StatCard = styled(Card)`
    ${tw`relative overflow-hidden`};
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    }
`;

const StatIcon = styled.div<{ $color: string }>`
    ${tw`w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl`};
    background: ${({ $color }) => $color};
    box-shadow: var(--shadow-md);
`;

const MetricChart = styled.div`
    ${tw`h-32 rounded-lg flex items-end justify-between p-4 space-x-1`};
    background: linear-gradient(135deg, var(--surface-secondary), var(--surface-tertiary));
`;

const ChartBar = styled.div<{ $height: number; $color: string }>`
    ${tw`rounded-t flex-1 transition-all duration-500`};
    height: ${({ $height }) => $height}%;
    background: ${({ $color }) => $color};
    min-height: 4px;
`;

const QuickActionButton = styled(motion.button)`
    ${tw`w-full p-4 rounded-lg border-2 border-dashed transition-all duration-200 text-left`};
    border-color: var(--border-secondary);
    background-color: var(--surface-primary);
    
    &:hover {
        border-color: var(--primary-500);
        background-color: var(--surface-hover);
        transform: translateY(-2px);
    }
`;

const AdminDashboard: React.FC = () => {
    const { colors } = useTheme();
    const [timeRange, setTimeRange] = useState('24h');

    // Mock data - in real implementation, this would come from API
    const stats = {
        totalServers: 156,
        activeServers: 142,
        totalUsers: 1247,
        activeUsers: 892,
        totalNodes: 8,
        healthyNodes: 7,
        totalDatabases: 89,
        activeDatabases: 85,
    };

    const systemMetrics = {
        cpu: [65, 72, 68, 75, 82, 78, 85, 79, 73, 77, 81, 76],
        memory: [78, 82, 85, 88, 92, 89, 94, 91, 87, 90, 93, 88],
        disk: [45, 47, 52, 49, 55, 58, 62, 59, 56, 61, 64, 60],
        network: [23, 28, 31, 26, 34, 38, 42, 39, 35, 41, 44, 37],
    };

    const quickActions = [
        {
            title: 'Create Server',
            description: 'Deploy a new game server',
            icon: faServer,
            color: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
            action: () => console.log('Create server'),
        },
        {
            title: 'Add User',
            description: 'Create a new user account',
            icon: faUsers,
            color: 'linear-gradient(135deg, #10b981, #047857)',
            action: () => console.log('Add user'),
        },
        {
            title: 'Node Management',
            description: 'Manage server nodes',
            icon: faNetworkWired,
            color: 'linear-gradient(135deg, #f59e0b, #d97706)',
            action: () => console.log('Node management'),
        },
        {
            title: 'Database Admin',
            description: 'Manage databases',
            icon: faDatabase,
            color: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
            action: () => console.log('Database admin'),
        },
        {
            title: 'System Settings',
            description: 'Configure panel settings',
            icon: faCog,
            color: 'linear-gradient(135deg, #6b7280, #4b5563)',
            action: () => console.log('System settings'),
        },
        {
            title: 'Backup & Restore',
            description: 'Manage system backups',
            icon: faDownload,
            color: 'linear-gradient(135deg, #ef4444, #dc2626)',
            action: () => console.log('Backup & restore'),
        },
    ];

    return (
        <AdminContainer>
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
            >
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-theme-primary mb-2">
                        Admin Dashboard
                    </h1>
                    <p className="text-theme-secondary">
                        Monitor and manage your Pterodactyl panel
                    </p>
                </div>

                {/* Stats Overview */}
                <StatsGrid>
                    <StatCard variant="elevated">
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-theme-tertiary">Total Servers</p>
                                    <p className="text-2xl font-bold text-theme-primary">{stats.totalServers}</p>
                                    <p className="text-xs text-green-500 mt-1">
                                        {stats.activeServers} active
                                    </p>
                                </div>
                                <StatIcon $color="linear-gradient(135deg, #3b82f6, #1d4ed8)">
                                    <FontAwesomeIcon icon={faServer} />
                                </StatIcon>
                            </div>
                        </CardContent>
                    </StatCard>

                    <StatCard variant="elevated">
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-theme-tertiary">Total Users</p>
                                    <p className="text-2xl font-bold text-theme-primary">{stats.totalUsers}</p>
                                    <p className="text-xs text-green-500 mt-1">
                                        {stats.activeUsers} active
                                    </p>
                                </div>
                                <StatIcon $color="linear-gradient(135deg, #10b981, #047857)">
                                    <FontAwesomeIcon icon={faUsers} />
                                </StatIcon>
                            </div>
                        </CardContent>
                    </StatCard>

                    <StatCard variant="elevated">
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-theme-tertiary">Nodes</p>
                                    <p className="text-2xl font-bold text-theme-primary">{stats.totalNodes}</p>
                                    <p className="text-xs text-green-500 mt-1">
                                        {stats.healthyNodes} healthy
                                    </p>
                                </div>
                                <StatIcon $color="linear-gradient(135deg, #f59e0b, #d97706)">
                                    <FontAwesomeIcon icon={faNetworkWired} />
                                </StatIcon>
                            </div>
                        </CardContent>
                    </StatCard>

                    <StatCard variant="elevated">
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-theme-tertiary">Databases</p>
                                    <p className="text-2xl font-bold text-theme-primary">{stats.totalDatabases}</p>
                                    <p className="text-xs text-green-500 mt-1">
                                        {stats.activeDatabases} active
                                    </p>
                                </div>
                                <StatIcon $color="linear-gradient(135deg, #8b5cf6, #7c3aed)">
                                    <FontAwesomeIcon icon={faDatabase} />
                                </StatIcon>
                            </div>
                        </CardContent>
                    </StatCard>
                </StatsGrid>

                {/* System Metrics */}
                <ChartsGrid>
                    <Card variant="elevated">
                        <CardHeader>
                            <CardTitle>System Performance</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-theme-secondary">CPU Usage</span>
                                        <span className="text-sm text-theme-tertiary">76%</span>
                                    </div>
                                    <MetricChart>
                                        {systemMetrics.cpu.map((value, index) => (
                                            <ChartBar
                                                key={index}
                                                $height={value}
                                                $color="linear-gradient(to top, #3b82f6, #60a5fa)"
                                            />
                                        ))}
                                    </MetricChart>
                                </div>
                                
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-theme-secondary">Memory Usage</span>
                                        <span className="text-sm text-theme-tertiary">88%</span>
                                    </div>
                                    <MetricChart>
                                        {systemMetrics.memory.map((value, index) => (
                                            <ChartBar
                                                key={index}
                                                $height={value}
                                                $color="linear-gradient(to top, #10b981, #34d399)"
                                            />
                                        ))}
                                    </MetricChart>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card variant="elevated">
                        <CardHeader>
                            <CardTitle>Resource Utilization</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-theme-secondary">Disk Usage</span>
                                        <span className="text-sm text-theme-tertiary">60%</span>
                                    </div>
                                    <MetricChart>
                                        {systemMetrics.disk.map((value, index) => (
                                            <ChartBar
                                                key={index}
                                                $height={value}
                                                $color="linear-gradient(to top, #f59e0b, #fbbf24)"
                                            />
                                        ))}
                                    </MetricChart>
                                </div>
                                
                                <div>
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-theme-secondary">Network I/O</span>
                                        <span className="text-sm text-theme-tertiary">37%</span>
                                    </div>
                                    <MetricChart>
                                        {systemMetrics.network.map((value, index) => (
                                            <ChartBar
                                                key={index}
                                                $height={value}
                                                $color="linear-gradient(to top, #8b5cf6, #a78bfa)"
                                            />
                                        ))}
                                    </MetricChart>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </ChartsGrid>

                {/* Quick Actions */}
                <div className="mb-8">
                    <h2 className="text-xl font-semibold text-theme-primary mb-4">Quick Actions</h2>
                    <QuickActionsGrid>
                        {quickActions.map((action, index) => (
                            <motion.div
                                key={action.title}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                            >
                                <QuickActionButton
                                    onClick={action.action}
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    <div className="flex items-center space-x-3">
                                        <div 
                                            className="w-10 h-10 rounded-lg flex items-center justify-center text-white"
                                            style={{ background: action.color }}
                                        >
                                            <FontAwesomeIcon icon={action.icon} />
                                        </div>
                                        <div>
                                            <h3 className="font-medium text-theme-primary">{action.title}</h3>
                                            <p className="text-sm text-theme-tertiary">{action.description}</p>
                                        </div>
                                    </div>
                                </QuickActionButton>
                            </motion.div>
                        ))}
                    </QuickActionsGrid>
                </div>
            </motion.div>
        </AdminContainer>
    );
};

export default AdminDashboard;
