import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import { ThemeMode, ThemeColors, darkTheme, lightTheme, animations, shadows, spacing } from '@/theme';

interface ThemeContextType {
    mode: ThemeMode;
    colors: ThemeColors;
    toggleTheme: () => void;
    setTheme: (mode: ThemeMode) => void;
    animations: typeof animations;
    shadows: typeof shadows;
    spacing: typeof spacing;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
    children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
    const [mode, setMode] = useState<ThemeMode>(() => {
        // Check localStorage for saved theme preference
        const savedTheme = localStorage.getItem('pterodactyl-theme');
        if (savedTheme === 'light' || savedTheme === 'dark') {
            return savedTheme;
        }
        
        // Check system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        
        return 'dark'; // Default to dark theme
    });

    const colors = mode === 'dark' ? darkTheme : lightTheme;

    const toggleTheme = () => {
        const newMode = mode === 'dark' ? 'light' : 'dark';
        setMode(newMode);
    };

    const setTheme = (newMode: ThemeMode) => {
        setMode(newMode);
    };

    // Save theme preference to localStorage
    useEffect(() => {
        localStorage.setItem('pterodactyl-theme', mode);
        
        // Update CSS custom properties for Tailwind
        const root = document.documentElement;
        
        // Update background colors
        root.style.setProperty('--bg-primary', colors.background.primary);
        root.style.setProperty('--bg-secondary', colors.background.secondary);
        root.style.setProperty('--bg-tertiary', colors.background.tertiary);
        root.style.setProperty('--bg-elevated', colors.background.elevated);
        
        // Update surface colors
        root.style.setProperty('--surface-primary', colors.surface.primary);
        root.style.setProperty('--surface-secondary', colors.surface.secondary);
        root.style.setProperty('--surface-tertiary', colors.surface.tertiary);
        root.style.setProperty('--surface-elevated', colors.surface.elevated);
        root.style.setProperty('--surface-hover', colors.surface.hover);
        root.style.setProperty('--surface-active', colors.surface.active);
        
        // Update text colors
        root.style.setProperty('--text-primary', colors.text.primary);
        root.style.setProperty('--text-secondary', colors.text.secondary);
        root.style.setProperty('--text-tertiary', colors.text.tertiary);
        root.style.setProperty('--text-disabled', colors.text.disabled);
        root.style.setProperty('--text-inverse', colors.text.inverse);
        
        // Update border colors
        root.style.setProperty('--border-primary', colors.border.primary);
        root.style.setProperty('--border-secondary', colors.border.secondary);
        root.style.setProperty('--border-tertiary', colors.border.tertiary);
        root.style.setProperty('--border-focus', colors.border.focus);
        
        // Update status colors
        root.style.setProperty('--status-success', colors.status.success);
        root.style.setProperty('--status-warning', colors.status.warning);
        root.style.setProperty('--status-error', colors.status.error);
        root.style.setProperty('--status-info', colors.status.info);
        
        // Update accent colors
        root.style.setProperty('--accent-cyan', colors.accent.cyan);
        root.style.setProperty('--accent-purple', colors.accent.purple);
        root.style.setProperty('--accent-pink', colors.accent.pink);
        root.style.setProperty('--accent-orange', colors.accent.orange);
        
        // Update primary colors
        root.style.setProperty('--primary-50', colors.primary[50]);
        root.style.setProperty('--primary-100', colors.primary[100]);
        root.style.setProperty('--primary-200', colors.primary[200]);
        root.style.setProperty('--primary-300', colors.primary[300]);
        root.style.setProperty('--primary-400', colors.primary[400]);
        root.style.setProperty('--primary-500', colors.primary[500]);
        root.style.setProperty('--primary-600', colors.primary[600]);
        root.style.setProperty('--primary-700', colors.primary[700]);
        root.style.setProperty('--primary-800', colors.primary[800]);
        root.style.setProperty('--primary-900', colors.primary[900]);
        
        // Update data-theme attribute for CSS selectors
        root.setAttribute('data-theme', mode);
    }, [mode, colors]);

    // Listen for system theme changes
    useEffect(() => {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = (e: MediaQueryListEvent) => {
            // Only update if user hasn't manually set a preference
            const savedTheme = localStorage.getItem('pterodactyl-theme');
            if (!savedTheme) {
                setMode(e.matches ? 'dark' : 'light');
            }
        };

        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
    }, []);

    const themeValue: ThemeContextType = {
        mode,
        colors,
        toggleTheme,
        setTheme,
        animations,
        shadows,
        spacing,
    };

    return (
        <ThemeContext.Provider value={themeValue}>
            <StyledThemeProvider theme={themeValue}>
                {children}
            </StyledThemeProvider>
        </ThemeContext.Provider>
    );
};

export const useTheme = (): ThemeContextType => {
    const context = useContext(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

// Hook for getting theme-aware styles
export const useThemeStyles = () => {
    const { colors, mode } = useTheme();
    
    return {
        colors,
        mode,
        isDark: mode === 'dark',
        isLight: mode === 'light',
    };
};
